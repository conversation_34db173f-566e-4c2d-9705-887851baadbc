{"name": "@workspace/vnc-viewer", "version": "0.0.1", "type": "module", "private": true, "exports": {"./components": "./src/components/index.ts", "./hooks": "./src/hooks/index.ts", "./utils": "./src/utils/index.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --max-warnings 0", "typecheck": "tsc --noEmit"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "@novnc/novnc": "^1.4.0"}, "devDependencies": {"@types/react": "^19", "@types/react-dom": "^19", "@types/node": "^20", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "typescript": "^5.7.3"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}