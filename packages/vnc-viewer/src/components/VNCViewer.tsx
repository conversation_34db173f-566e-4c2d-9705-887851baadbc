"use client"

import React, { forwardRef, useImperativeHandle, useEffect, useState } from 'react';
// import { useVNCViewer } from '../hooks/useVNCViewer';
import type { VNCViewerProps, VNCViewerRef } from '../types/index';

export const VNCViewer = forwardRef<VNCViewerRef, VNCViewerProps>(({
  url,
  credentials,
  className = '',
  style,
  width,
  height,
  autoConnect = true,
  reconnectDelay = 3000,
  maxReconnectAttempts = 5,
  viewOnly = false,
  focusOnClick = true,
  clipViewport = false,
  dragViewport = true,
  scaleViewport = false,
  resizeSession = false,
  showDotCursor = false,
  background = '#000000',
  qualityLevel = 6,
  compressionLevel = 2,
  onConnect,
  onDisconnect,
  onCredentialsRequired,
  onSecurityFailure,
  onClipboard,
  onBell,
  onDesktopName,
  onCapabilities,
}, ref) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('Disconnected');

  // Mock VNC viewer for now - will be replaced with real implementation
  const connect = () => {
    setConnectionStatus('Connecting...');
    setTimeout(() => {
      setIsConnected(true);
      setConnectionStatus('Connected');
      onConnect?.();
    }, 2000);
  };

  const disconnect = () => {
    setIsConnected(false);
    setConnectionStatus('Disconnected');
    onDisconnect?.(true);
  };

  const sendCredentials = (creds: any) => {
    console.log('Send credentials:', creds);
  };

  const sendKey = (keysym: number, code: string, down: boolean = true) => {
    console.log('Send key:', keysym, code, down);
  };

  const sendCtrlAltDel = () => {
    console.log('Send Ctrl+Alt+Del');
  };

  const clipboardPasteFrom = (text: string) => {
    console.log('Clipboard paste:', text);
  };

  const machineShutdown = () => {
    console.log('Machine shutdown');
  };

  const machineReboot = () => {
    console.log('Machine reboot');
  };

  const machineReset = () => {
    console.log('Machine reset');
  };

  const getState = () => ({
    connected: isConnected,
    connecting: connectionStatus === 'Connecting...',
    disconnected: !isConnected,
    error: null,
    rfbVersion: null,
    serverName: isConnected ? 'Mock VNC Server' : null,
  });

  // Expose methods through ref
  useImperativeHandle(ref, () => ({
    connect,
    disconnect,
    sendCredentials,
    sendKey,
    sendCtrlAltDel,
    clipboardPasteFrom,
    machineShutdown,
    machineReboot,
    machineReset,
    getState: () => state,
  }), [
    connect,
    disconnect,
    sendCredentials,
    sendKey,
    sendCtrlAltDel,
    clipboardPasteFrom,
    machineShutdown,
    machineReboot,
    machineReset,
    state,
  ]);

  // Handle container resize
  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current?.parentElement) {
        const parent = canvasRef.current.parentElement;
        setContainerSize({
          width: parent.clientWidth,
          height: parent.clientHeight,
        });
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width: width || '100%',
    height: height || '100%',
    backgroundColor: background,
    overflow: 'hidden',
    ...style,
  };

  const canvasStyle: React.CSSProperties = {
    display: 'block',
    margin: '0 auto',
  };

  return (
    <div className={`vnc-viewer ${className}`} style={containerStyle}>
      {/* Connection Status Overlay */}
      {(state.connecting || state.error || state.disconnected) && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            zIndex: 10,
            flexDirection: 'column',
            gap: '1rem',
          }}
        >
          {state.connecting && (
            <>
              <div
                style={{
                  width: '40px',
                  height: '40px',
                  border: '4px solid #f3f3f3',
                  borderTop: '4px solid #3498db',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite',
                }}
              />
              <div>Connecting to VNC server...</div>
            </>
          )}

          {state.error && (
            <>
              <div style={{ fontSize: '2rem' }}>⚠️</div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontWeight: 'bold', marginBottom: '0.5rem' }}>
                  Connection Error
                </div>
                <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>
                  {state.error}
                </div>
                <button
                  onClick={connect}
                  style={{
                    marginTop: '1rem',
                    padding: '0.5rem 1rem',
                    backgroundColor: '#3498db',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                  }}
                >
                  Retry Connection
                </button>
              </div>
            </>
          )}

          {state.disconnected && !state.error && !state.connecting && (
            <>
              <div style={{ fontSize: '2rem' }}>🔌</div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontWeight: 'bold', marginBottom: '0.5rem' }}>
                  Disconnected
                </div>
                <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>
                  Click to connect to VNC server
                </div>
                <button
                  onClick={connect}
                  style={{
                    marginTop: '1rem',
                    padding: '0.5rem 1rem',
                    backgroundColor: '#27ae60',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                  }}
                >
                  Connect
                </button>
              </div>
            </>
          )}
        </div>
      )}

      {/* VNC Canvas */}
      <canvas
        ref={canvasRef}
        style={canvasStyle}
        tabIndex={0}
        onContextMenu={(e) => e.preventDefault()}
      />

      {/* Connection Info */}
      {state.connected && state.serverName && (
        <div
          style={{
            position: 'absolute',
            top: '10px',
            left: '10px',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            padding: '0.5rem',
            borderRadius: '4px',
            fontSize: '0.8rem',
            zIndex: 5,
          }}
        >
          {state.serverName}
        </div>
      )}

      {/* CSS for spinner animation */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
});

VNCViewer.displayName = 'VNCViewer';
