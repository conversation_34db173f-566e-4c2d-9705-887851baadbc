import { useEffect, useRef, useState, useCallback } from 'react';
import type {
  VNCViewerState,
  VNCViewerOptions,
  VNCViewerEvents,
  VNCCredentials
} from '../types/index';
import {
  buildVNCUrl,
  validateVNCConfig,
  formatVNCError,
  checkBrowserSupport
} from '../utils/vncUtils';

interface UseVNCViewerProps extends VNCViewerOptions, VNCViewerEvents {
  url: string;
  credentials?: VNCCredentials;
  autoConnect?: boolean;
  reconnectDelay?: number;
  maxReconnectAttempts?: number;
}

export function useVNCViewer({
  url,
  credentials,
  autoConnect = true,
  reconnectDelay = 3000,
  maxReconnectAttempts = 5,
  viewOnly = false,
  focusOnClick = true,
  clipViewport = false,
  dragViewport = true,
  scaleViewport = false,
  resizeSession = false,
  showDotCursor = false,
  background = '#000000',
  qualityLevel = 6,
  compressionLevel = 2,
  onConnect,
  onDisconnect,
  onCredentialsRequired,
  onSecurityFailure,
  onClipboard,
  onBell,
  onDesktopName,
  onCapabilities,
}: UseVNCViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const rfbRef = useRef<any>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttemptsRef = useRef(0);

  const [state, setState] = useState<VNCViewerState>({
    connected: false,
    connecting: false,
    disconnected: true,
    error: null,
    rfbVersion: null,
    serverName: null,
  });

  // Check browser support on mount
  useEffect(() => {
    const support = checkBrowserSupport();
    if (!support.supported) {
      setState(prev => ({
        ...prev,
        error: `Browser missing required features: ${support.missing.join(', ')}`
      }));
    }
  }, []);

  // Initialize noVNC when canvas is available
  useEffect(() => {
    if (!canvasRef.current || state.error) return;

    const initializeVNC = async () => {
      try {
        // Dynamically import noVNC to avoid SSR issues
        const { default: RFB } = await import('@novnc/novnc/core/rfb');

        const rfb = new RFB(canvasRef.current, url, {
          credentials: credentials || {},
          repeaterID: '',
          shared: true,
          wsProtocols: ['binary'],
        });

        // Configure RFB options
        rfb.viewOnly = viewOnly;
        rfb.focusOnClick = focusOnClick;
        rfb.clipViewport = clipViewport;
        rfb.dragViewport = dragViewport;
        rfb.scaleViewport = scaleViewport;
        rfb.resizeSession = resizeSession;
        rfb.showDotCursor = showDotCursor;
        rfb.background = background;
        rfb.qualityLevel = qualityLevel;
        rfb.compressionLevel = compressionLevel;

        // Set up event listeners
        rfb.addEventListener('connect', () => {
          setState(prev => ({
            ...prev,
            connected: true,
            connecting: false,
            disconnected: false,
            error: null,
          }));
          reconnectAttemptsRef.current = 0;
          onConnect?.();
        });

        rfb.addEventListener('disconnect', (e: any) => {
          const clean = e.detail.clean;
          setState(prev => ({
            ...prev,
            connected: false,
            connecting: false,
            disconnected: true,
            error: clean ? null : 'Connection lost',
          }));

          onDisconnect?.(clean);

          // Auto-reconnect if not a clean disconnect
          if (!clean && reconnectAttemptsRef.current < maxReconnectAttempts) {
            reconnectAttemptsRef.current++;
            reconnectTimeoutRef.current = setTimeout(() => {
              if (rfbRef.current) {
                connect();
              }
            }, reconnectDelay);
          }
        });

        rfb.addEventListener('credentialsrequired', (e: any) => {
          setState(prev => ({ ...prev, connecting: false }));
          onCredentialsRequired?.(e.detail);
        });

        rfb.addEventListener('securityfailure', (e: any) => {
          setState(prev => ({
            ...prev,
            connecting: false,
            error: 'Security failure: ' + e.detail,
          }));
          onSecurityFailure?.(reconnectAttemptsRef.current, e.detail);
        });

        rfb.addEventListener('clipboard', (e: any) => {
          onClipboard?.(e.detail.text);
        });

        rfb.addEventListener('bell', () => {
          onBell?.();
        });

        rfb.addEventListener('desktopname', (e: any) => {
          setState(prev => ({ ...prev, serverName: e.detail.name }));
          onDesktopName?.(e.detail.name);
        });

        rfb.addEventListener('capabilities', (e: any) => {
          onCapabilities?.(e.detail);
        });

        rfbRef.current = rfb;

        if (autoConnect) {
          connect();
        }
      } catch (error) {
        setState(prev => ({
          ...prev,
          error: formatVNCError(error),
          connecting: false,
        }));
      }
    };

    initializeVNC();

    return () => {
      disconnect();
    };
  }, [url, autoConnect]);

  const connect = useCallback(() => {
    if (!rfbRef.current || state.connected || state.connecting) return;

    setState(prev => ({
      ...prev,
      connecting: true,
      error: null,
    }));

    try {
      // RFB connection is handled automatically when RFB is created
      // This function is mainly for manual reconnection
    } catch (error) {
      setState(prev => ({
        ...prev,
        connecting: false,
        error: formatVNCError(error),
      }));
    }
  }, [state.connected, state.connecting]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    if (rfbRef.current) {
      rfbRef.current.disconnect();
      rfbRef.current = null;
    }

    setState(prev => ({
      ...prev,
      connected: false,
      connecting: false,
      disconnected: true,
    }));
  }, []);

  const sendCredentials = useCallback((creds: VNCCredentials) => {
    if (rfbRef.current) {
      rfbRef.current.sendCredentials(creds);
    }
  }, []);

  const sendKey = useCallback((keysym: number, code: string, down: boolean = true) => {
    if (rfbRef.current && state.connected) {
      rfbRef.current.sendKey(keysym, code, down);
    }
  }, [state.connected]);

  const sendCtrlAltDel = useCallback(() => {
    if (rfbRef.current && state.connected) {
      rfbRef.current.sendCtrlAltDel();
    }
  }, [state.connected]);

  const clipboardPasteFrom = useCallback((text: string) => {
    if (rfbRef.current && state.connected) {
      rfbRef.current.clipboardPasteFrom(text);
    }
  }, [state.connected]);

  const machineShutdown = useCallback(() => {
    if (rfbRef.current && state.connected) {
      rfbRef.current.machineShutdown();
    }
  }, [state.connected]);

  const machineReboot = useCallback(() => {
    if (rfbRef.current && state.connected) {
      rfbRef.current.machineReboot();
    }
  }, [state.connected]);

  const machineReset = useCallback(() => {
    if (rfbRef.current && state.connected) {
      rfbRef.current.machineReset();
    }
  }, [state.connected]);

  return {
    canvasRef,
    state,
    connect,
    disconnect,
    sendCredentials,
    sendKey,
    sendCtrlAltDel,
    clipboardPasteFrom,
    machineShutdown,
    machineReboot,
    machineReset,
  };
}
