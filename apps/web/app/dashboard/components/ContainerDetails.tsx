"use client"

import { useState } from "react"
import { useMutation, useQuery } from "@tanstack/react-query"
import { 
  Play, 
  Square, 
  Trash2, 
  Upload, 
  GitBranch, 
  Monitor, 
  Activity,
  ExternalLink,
  Loader2
} from "lucide-react"

import { Button } from "@workspace/ui/components/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card"
import { Badge } from "@workspace/ui/components/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs"
import { Alert, AlertDescription } from "@workspace/ui/components/alert"

import { containerApi, type Container } from "@/lib/api"
// import { DeployAppDialog } from "./DeployAppDialog"

interface ContainerDetailsProps {
  container: Container
  onUpdate: () => void
}

export function ContainerDetails({ container, onUpdate }: ContainerDetailsProps) {
  const [isDeployDialogOpen, setIsDeployDialogOpen] = useState(false)

  // Fetch VNC connection info
  const { data: vncData } = useQuery({
    queryKey: ['vnc', container.id],
    queryFn: () => containerApi.getVNC(container.id),
    enabled: container.status === 'running',
  })

  // Fetch container stats
  const { data: statsData } = useQuery({
    queryKey: ['stats', container.id],
    queryFn: () => containerApi.getStats(container.id),
    enabled: container.status === 'running',
    refetchInterval: 5000,
  })

  // Mutations
  const deleteMutation = useMutation({
    mutationFn: () => containerApi.delete(container.id),
    onSuccess: onUpdate,
  })

  const startAppMutation = useMutation({
    mutationFn: () => containerApi.start(container.id),
    onSuccess: onUpdate,
  })

  const stopAppMutation = useMutation({
    mutationFn: () => containerApi.stop(container.id),
    onSuccess: onUpdate,
  })

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this container? This action cannot be undone.')) {
      deleteMutation.mutate()
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-600'
      case 'stopped': return 'text-gray-600'
      case 'creating': return 'text-blue-600'
      case 'deploying': return 'text-blue-600'
      case 'error': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {container.name}
                <Badge variant={container.status === 'running' ? 'default' : 'secondary'}>
                  {container.status}
                </Badge>
              </CardTitle>
              <CardDescription>
                Node.js {container.nodeVersion} • {container.desktopEnvironment.toUpperCase()} Desktop
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {vncData?.data && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(vncData.data.webUrl, '_blank')}
                >
                  <Monitor className="w-4 h-4 mr-2" />
                  Open Desktop
                </Button>
              )}
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                disabled={deleteMutation.isPending}
              >
                {deleteMutation.isPending ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4 mr-2" />
                )}
                Delete
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="application">Application</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Container Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className={getStatusColor(container.status)}>{container.status}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Node.js:</span>
                  <span>{container.nodeVersion}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Desktop:</span>
                  <span className="capitalize">{container.desktopEnvironment}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Created:</span>
                  <span>{new Date(container.createdAt).toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Ports</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">VNC:</span>
                  <span>{container.ports.vnc}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">noVNC:</span>
                  <span>{container.ports.noVNC}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Application:</span>
                  <span>{container.ports.app}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {container.status === 'running' && vncData?.data && (
            <Alert>
              <Monitor className="h-4 w-4" />
              <AlertDescription>
                Container is running! You can access the desktop environment at{' '}
                <a 
                  href={vncData.data.webUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  {vncData.data.webUrl}
                </a>
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="application" className="space-y-4">
          {container.application ? (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {container.application.name}
                        <Badge variant={container.application.status === 'running' ? 'default' : 'secondary'}>
                          {container.application.status}
                        </Badge>
                      </CardTitle>
                      <CardDescription>
                        Running on port {container.application.port}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      {container.application.status === 'running' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`http://localhost:${container.application?.port}`, '_blank')}
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Open App
                        </Button>
                      )}
                      {container.application.status === 'running' ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => stopAppMutation.mutate()}
                          disabled={stopAppMutation.isPending}
                        >
                          <Square className="w-4 h-4 mr-2" />
                          Stop
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => startAppMutation.mutate()}
                          disabled={startAppMutation.isPending}
                        >
                          <Play className="w-4 h-4 mr-2" />
                          Start
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <Upload className="w-16 h-16 mx-auto mb-4" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No Application Deployed
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Deploy a Node.js application to this container to get started
                </p>
                <div className="flex justify-center gap-2">
                  <Button onClick={() => setIsDeployDialogOpen(true)}>
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Files
                  </Button>
                  <Button variant="outline" onClick={() => setIsDeployDialogOpen(true)}>
                    <GitBranch className="w-4 h-4 mr-2" />
                    Clone from Git
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          {container.application ? (
            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Build Logs</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
                    {container.application.buildLogs.length > 0 ? (
                      container.application.buildLogs.map((log, index) => (
                        <div key={index}>{log}</div>
                      ))
                    ) : (
                      <div className="text-gray-500">No build logs available</div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Runtime Logs</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
                    {container.application.runtimeLogs.length > 0 ? (
                      container.application.runtimeLogs.map((log, index) => (
                        <div key={index}>{log}</div>
                      ))
                    ) : (
                      <div className="text-gray-500">No runtime logs available</div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <p className="text-gray-600 dark:text-gray-300">
                  Deploy an application to view logs
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          {statsData?.data ? (
            <div className="grid md:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    CPU Usage
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statsData.data.cpu.toFixed(1)}%
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Memory Usage</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {(statsData.data.memory / 1024 / 1024).toFixed(0)} MB
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Network</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1">
                    <div className="text-sm">
                      RX: {(statsData.data.network.rx / 1024).toFixed(0)} KB
                    </div>
                    <div className="text-sm">
                      TX: {(statsData.data.network.tx / 1024).toFixed(0)} KB
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <p className="text-gray-600 dark:text-gray-300">
                  Statistics available when container is running
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* TODO: Add DeployAppDialog component */}
    </div>
  )
}
