import Docker from 'dockerode';
import { v4 as uuidv4 } from 'uuid';
import { config } from '@/config/index.js';
import { Logger } from '@/utils/logger.js';
import { portManager } from '@/utils/portManager.js';
import type { Container, CreateContainerRequest, ContainerStats, VNCConnection } from '@/types/index.js';

export class ContainerService {
  private docker: Docker;
  private containers = new Map<string, Container>();

  constructor() {
    this.docker = new Docker({ socketPath: config.docker.socketPath });
    this.initializeNetwork();
  }

  private async initializeNetwork(): Promise<void> {
    try {
      const networks = await this.docker.listNetworks();
      const networkExists = networks.some(net => net.Name === config.docker.network);
      
      if (!networkExists) {
        await this.docker.createNetwork({
          Name: config.docker.network,
          Driver: 'bridge',
        });
        Logger.info(`Created Docker network: ${config.docker.network}`);
      }
    } catch (error) {
      Logger.error('Failed to initialize Docker network', { error });
    }
  }

  async createContainer(request: CreateContainerRequest): Promise<Container> {
    if (this.containers.size >= config.container.maxContainers) {
      throw new Error('Maximum number of containers reached');
    }

    const containerId = uuidv4();
    const vncPort = portManager.allocateVNCPort();
    const noVNCPort = portManager.allocateNoVNCPort();
    const appPort = portManager.allocateAppPort();

    const container: Container = {
      id: containerId,
      name: request.name,
      status: 'creating',
      nodeVersion: request.nodeVersion || config.container.defaultNodeVersion,
      desktopEnvironment: request.desktopEnvironment || config.container.defaultDesktop,
      createdAt: new Date(),
      updatedAt: new Date(),
      ports: {
        vnc: vncPort,
        noVNC: noVNCPort,
        app: appPort,
      },
    };

    this.containers.set(containerId, container);

    try {
      await this.startDockerContainer(container);
      container.status = 'running';
      container.updatedAt = new Date();
      
      Logger.info(`Container created successfully`, { containerId, name: request.name });
      return container;
    } catch (error) {
      container.status = 'error';
      container.updatedAt = new Date();
      
      // Release allocated ports on failure
      portManager.releasePorts([vncPort, noVNCPort, appPort]);
      
      Logger.error('Failed to create container', { containerId, error });
      throw error;
    }
  }

  private async startDockerContainer(container: Container): Promise<void> {
    const dockerContainer = await this.docker.createContainer({
      Image: `${config.docker.baseImage}:latest`,
      name: `container-runner-${container.id}`,
      Env: [
        `VNC_PORT=${container.ports.vnc}`,
        `NOVNC_PORT=${container.ports.noVNC}`,
        `APP_PORT=${container.ports.app}`,
        `NODE_VERSION=${container.nodeVersion}`,
        `DESKTOP=${container.desktopEnvironment}`,
      ],
      ExposedPorts: {
        [`${container.ports.vnc}/tcp`]: {},
        [`${container.ports.noVNC}/tcp`]: {},
        [`${container.ports.app}/tcp`]: {},
      },
      HostConfig: {
        PortBindings: {
          [`${container.ports.vnc}/tcp`]: [{ HostPort: container.ports.vnc.toString() }],
          [`${container.ports.noVNC}/tcp`]: [{ HostPort: container.ports.noVNC.toString() }],
          [`${container.ports.app}/tcp`]: [{ HostPort: container.ports.app.toString() }],
        },
        NetworkMode: config.docker.network,
        Memory: 1024 * 1024 * 1024, // 1GB
        CpuShares: 512,
      },
      WorkingDir: '/workspace',
    });

    await dockerContainer.start();
  }

  async getContainer(id: string): Promise<Container | null> {
    return this.containers.get(id) || null;
  }

  async listContainers(): Promise<Container[]> {
    return Array.from(this.containers.values());
  }

  async deleteContainer(id: string): Promise<void> {
    const container = this.containers.get(id);
    if (!container) {
      throw new Error('Container not found');
    }

    try {
      const dockerContainer = this.docker.getContainer(`container-runner-${id}`);
      await dockerContainer.stop();
      await dockerContainer.remove();
      
      // Release ports
      portManager.releasePorts([
        container.ports.vnc,
        container.ports.noVNC,
        container.ports.app,
      ]);
      
      this.containers.delete(id);
      Logger.info(`Container deleted successfully`, { containerId: id });
    } catch (error) {
      Logger.error('Failed to delete container', { containerId: id, error });
      throw error;
    }
  }

  async getContainerStats(id: string): Promise<ContainerStats> {
    const dockerContainer = this.docker.getContainer(`container-runner-${id}`);
    const stats = await dockerContainer.stats({ stream: false });
    
    return {
      cpu: this.calculateCpuPercent(stats),
      memory: stats.memory_stats.usage || 0,
      network: {
        rx: stats.networks?.eth0?.rx_bytes || 0,
        tx: stats.networks?.eth0?.tx_bytes || 0,
      },
    };
  }

  private calculateCpuPercent(stats: any): number {
    const cpuDelta = stats.cpu_stats.cpu_usage.total_usage - stats.precpu_stats.cpu_usage.total_usage;
    const systemDelta = stats.cpu_stats.system_cpu_usage - stats.precpu_stats.system_cpu_usage;
    const numberCpus = stats.cpu_stats.online_cpus || 1;
    
    if (systemDelta > 0 && cpuDelta > 0) {
      return (cpuDelta / systemDelta) * numberCpus * 100;
    }
    return 0;
  }

  getVNCConnection(id: string): VNCConnection {
    const container = this.containers.get(id);
    if (!container) {
      throw new Error('Container not found');
    }

    return {
      host: config.host,
      port: container.ports.vnc,
      webUrl: `http://${config.host}:${container.ports.noVNC}`,
    };
  }
}
