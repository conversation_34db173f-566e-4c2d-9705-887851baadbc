import { Router } from 'express';
import multer from 'multer';
import <PERSON><PERSON> from 'joi';
import { ContainerService } from '@/services/ContainerService.js';
import { NodeAppService } from '@/services/NodeAppService.js';
import { Logger } from '@/utils/logger.js';
import { config } from '@/config/index.js';
import type { CreateContainerRequest, DeployAppRequest, ApiResponse } from '@/types/index.js';

const router = Router();
const containerService = new ContainerService();
const nodeAppService = new NodeAppService();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: config.upload.maxSize,
  },
  fileFilter: (req, file, cb) => {
    const ext = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
    if (config.upload.allowedExtensions.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${ext} not allowed`));
    }
  },
});

// Validation schemas
const createContainerSchema = Joi.object({
  name: Joi.string().min(1).max(50).required(),
  nodeVersion: Joi.string().valid('18', '20', '22').optional(),
  desktopEnvironment: Joi.string().valid('xfce', 'lxde').optional(),
});

const deployAppSchema = Joi.object({
  type: Joi.string().valid('upload', 'git').required(),
  gitUrl: Joi.string().uri().when('type', { is: 'git', then: Joi.required() }),
  gitBranch: Joi.string().optional(),
  buildCommand: Joi.string().optional(),
  startCommand: Joi.string().optional(),
  port: Joi.number().integer().min(1000).max(9999).optional(),
});

// GET /api/containers - List all containers
router.get('/', async (req, res) => {
  try {
    const containers = await containerService.listContainers();
    const response: ApiResponse = {
      success: true,
      data: containers,
    };
    res.json(response);
  } catch (error) {
    Logger.error('Failed to list containers', { error });
    const response: ApiResponse = {
      success: false,
      error: 'Failed to list containers',
    };
    res.status(500).json(response);
  }
});

// POST /api/containers - Create new container
router.post('/', async (req, res) => {
  try {
    const { error, value } = createContainerSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message,
      };
      return res.status(400).json(response);
    }

    const container = await containerService.createContainer(value as CreateContainerRequest);
    const response: ApiResponse = {
      success: true,
      data: container,
      message: 'Container created successfully',
    };
    res.status(201).json(response);
  } catch (error) {
    Logger.error('Failed to create container', { error });
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create container',
    };
    res.status(500).json(response);
  }
});

// GET /api/containers/:id - Get container details
router.get('/:id', async (req, res) => {
  try {
    const container = await containerService.getContainer(req.params.id);
    if (!container) {
      const response: ApiResponse = {
        success: false,
        error: 'Container not found',
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: container,
    };
    res.json(response);
  } catch (error) {
    Logger.error('Failed to get container', { containerId: req.params.id, error });
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get container',
    };
    res.status(500).json(response);
  }
});

// DELETE /api/containers/:id - Delete container
router.delete('/:id', async (req, res) => {
  try {
    await containerService.deleteContainer(req.params.id);
    const response: ApiResponse = {
      success: true,
      message: 'Container deleted successfully',
    };
    res.json(response);
  } catch (error) {
    Logger.error('Failed to delete container', { containerId: req.params.id, error });
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete container',
    };
    res.status(500).json(response);
  }
});

// POST /api/containers/:id/deploy - Deploy application
router.post('/:id/deploy', upload.array('files'), async (req, res) => {
  try {
    const container = await containerService.getContainer(req.params.id);
    if (!container) {
      const response: ApiResponse = {
        success: false,
        error: 'Container not found',
      };
      return res.status(404).json(response);
    }

    const { error, value } = deployAppSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message,
      };
      return res.status(400).json(response);
    }

    await nodeAppService.deployApplication(
      container,
      value as DeployAppRequest,
      req.files as Express.Multer.File[]
    );

    const response: ApiResponse = {
      success: true,
      message: 'Application deployment started',
      data: container,
    };
    res.json(response);
  } catch (error) {
    Logger.error('Failed to deploy application', { containerId: req.params.id, error });
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to deploy application',
    };
    res.status(500).json(response);
  }
});

// POST /api/containers/:id/start - Start application
router.post('/:id/start', async (req, res) => {
  try {
    const container = await containerService.getContainer(req.params.id);
    if (!container) {
      const response: ApiResponse = {
        success: false,
        error: 'Container not found',
      };
      return res.status(404).json(response);
    }

    await nodeAppService.startApplication(container, req.body.startCommand);
    const response: ApiResponse = {
      success: true,
      message: 'Application started',
      data: container,
    };
    res.json(response);
  } catch (error) {
    Logger.error('Failed to start application', { containerId: req.params.id, error });
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to start application',
    };
    res.status(500).json(response);
  }
});

// POST /api/containers/:id/stop - Stop application
router.post('/:id/stop', async (req, res) => {
  try {
    const container = await containerService.getContainer(req.params.id);
    if (!container) {
      const response: ApiResponse = {
        success: false,
        error: 'Container not found',
      };
      return res.status(404).json(response);
    }

    await nodeAppService.stopApplication(container);
    const response: ApiResponse = {
      success: true,
      message: 'Application stopped',
      data: container,
    };
    res.json(response);
  } catch (error) {
    Logger.error('Failed to stop application', { containerId: req.params.id, error });
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to stop application',
    };
    res.status(500).json(response);
  }
});

// GET /api/containers/:id/stats - Get container statistics
router.get('/:id/stats', async (req, res) => {
  try {
    const container = await containerService.getContainer(req.params.id);
    if (!container) {
      const response: ApiResponse = {
        success: false,
        error: 'Container not found',
      };
      return res.status(404).json(response);
    }

    const stats = await containerService.getContainerStats(req.params.id);
    const response: ApiResponse = {
      success: true,
      data: stats,
    };
    res.json(response);
  } catch (error) {
    Logger.error('Failed to get container stats', { containerId: req.params.id, error });
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get container stats',
    };
    res.status(500).json(response);
  }
});

// GET /api/containers/:id/vnc - Get VNC connection info
router.get('/:id/vnc', async (req, res) => {
  try {
    const container = await containerService.getContainer(req.params.id);
    if (!container) {
      const response: ApiResponse = {
        success: false,
        error: 'Container not found',
      };
      return res.status(404).json(response);
    }

    const vncConnection = containerService.getVNCConnection(req.params.id);
    const response: ApiResponse = {
      success: true,
      data: vncConnection,
    };
    res.json(response);
  } catch (error) {
    Logger.error('Failed to get VNC connection', { containerId: req.params.id, error });
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get VNC connection',
    };
    res.status(500).json(response);
  }
});

export default router;
