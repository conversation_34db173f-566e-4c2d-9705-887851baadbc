# Multi-stage Dockerfile for Node.js + Desktop Environment + Browser
ARG NODE_VERSION=20
ARG DESKTOP=xfce

# Base stage with Ubuntu
FROM ubuntu:22.04 as base

# Avoid prompts from apt
ENV DEBIAN_FRONTEND=noninteractive

# Install basic dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    gnupg \
    lsb-release \
    software-properties-common \
    ca-certificates \
    apt-transport-https \
    && rm -rf /var/lib/apt/lists/*

# Node.js installation stage
FROM base as nodejs
ARG NODE_VERSION

# Install Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g npm@latest \
    && node --version \
    && npm --version

# Desktop environment stage
FROM nodejs as desktop-base

# Install common desktop dependencies
RUN apt-get update && apt-get install -y \
    xorg \
    xserver-xorg-video-dummy \
    x11vnc \
    xvfb \
    fluxbox \
    wmctrl \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# XFCE desktop stage
FROM desktop-base as desktop-xfce
RUN apt-get update && apt-get install -y \
    xfce4 \
    xfce4-terminal \
    xfce4-goodies \
    && rm -rf /var/lib/apt/lists/*

# LXDE desktop stage  
FROM desktop-base as desktop-lxde
RUN apt-get update && apt-get install -y \
    lxde-core \
    lxterminal \
    && rm -rf /var/lib/apt/lists/*

# Browser installation stage
FROM desktop-${DESKTOP} as browser

# Install Google Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# noVNC installation stage
FROM browser as novnc

# Install noVNC
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /opt
RUN git clone https://github.com/novnc/noVNC.git \
    && git clone https://github.com/novnc/websockify.git \
    && cd websockify && python3 setup.py install

# Final stage
FROM novnc as final

# Create user
RUN useradd -m -s /bin/bash developer \
    && usermod -aG sudo developer \
    && echo "developer:developer" | chpasswd

# Set up workspace
WORKDIR /workspace
RUN chown developer:developer /workspace

# Copy configuration files
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY start-desktop.sh /usr/local/bin/start-desktop.sh
COPY xorg.conf /etc/X11/xorg.conf

# Make scripts executable
RUN chmod +x /usr/local/bin/start-desktop.sh

# Environment variables
ENV DISPLAY=:1
ENV VNC_PORT=5901
ENV NOVNC_PORT=6081
ENV APP_PORT=3000
ENV NODE_VERSION=${NODE_VERSION}
ENV DESKTOP=${DESKTOP}

# Expose ports
EXPOSE 5901 6081 3000

# Switch to developer user
USER developer

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
